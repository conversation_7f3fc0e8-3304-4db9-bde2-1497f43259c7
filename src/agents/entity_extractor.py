"""Entity extraction agent for machinery, subcomponents, and spare parts."""

import json
import re
from typing import List, Dict, Any, Optional, Tuple
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.schema import HumanMessage, SystemMessage
from loguru import logger

from ..models.entities import Machinery, Subcomponent, SparePart, MaintenanceJob, DocumentSection
from ..config import settings, ENTITY_EXTRACTION_PROMPTS


class EntityExtractorAgent:
    """Agent for extracting structured entities from document sections."""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens,
            openai_api_key=settings.openai_api_key
        )
        
        # Compile regex patterns for entity detection
        self.part_number_patterns = [
            r'\b[A-Z0-9]{3,}-[A-Z0-9]{2,}\b',  # ABC123-45
            r'\b[A-Z]{2,}\d{4,}\b',             # AB1234
            r'\b\d{4,}-[A-Z0-9]{2,}\b',        # 1234-AB
            r'\bP/N:?\s*([A-Z0-9\-]{4,})\b',   # P/N: ABC-123
            r'\bPart\s*No\.?:?\s*([A-Z0-9\-]{4,})\b'  # Part No: ABC-123
        ]
        
        self.specification_patterns = {
            'pressure': r'(\d+(?:\.\d+)?)\s*(bar|psi|kpa|mpa|atm)',
            'temperature': r'(\d+(?:\.\d+)?)\s*°?([CF]|celsius|fahrenheit)',
            'size': r'(\d+(?:\.\d+)?)\s*(mm|cm|m|inch|in|ft)',
            'weight': r'(\d+(?:\.\d+)?)\s*(kg|g|lb|ton)',
            'power': r'(\d+(?:\.\d+)?)\s*(kw|hp|w)',
            'flow': r'(\d+(?:\.\d+)?)\s*(l/min|gpm|m³/h)'
        }
    
    def extract_entities_from_section(self, section: DocumentSection) -> Dict[str, List]:
        """Extract all entity types from a document section."""
        try:
            logger.info(f"Extracting entities from section {section.section_id}")

            results = {
                "machinery": [],
                "subcomponents": [],
                "spare_parts": [],
                "maintenance_jobs": []
            }

            # Extract based on section type
            if section.section_type in ["MACHINERY_OVERVIEW", "TECHNICAL_SPECIFICATIONS"]:
                results["machinery"] = self.extract_machinery(section)
                results["subcomponents"] = self.extract_subcomponents(section)

            elif section.section_type in ["SUBCOMPONENT_DETAILS"]:
                results["subcomponents"] = self.extract_subcomponents(section)
                results["spare_parts"] = self.extract_spare_parts(section)

            elif section.section_type in ["SPARE_PARTS_LIST"]:
                results["spare_parts"] = self.extract_spare_parts(section)

            elif section.section_type in ["MAINTENANCE", "INSPECTION", "OVERHAUL"]:
                results["maintenance_jobs"] = self.extract_maintenance_jobs(section)

            else:
                # Try all extraction types for other sections
                results["machinery"] = self.extract_machinery(section)
                results["subcomponents"] = self.extract_subcomponents(section)
                results["spare_parts"] = self.extract_spare_parts(section)
                results["maintenance_jobs"] = self.extract_maintenance_jobs(section)

            # Add section reference to all entities
            for entity_type in results:
                for entity in results[entity_type]:
                    entity.page_reference = section.page_start
                    entity.raw_text = section.content[:200] + "..." if len(section.content) > 200 else section.content

            total_entities = sum(len(entities) for entities in results.values())
            logger.info(f"Extracted {total_entities} entities from section {section.section_id}")

            return results
            
        except Exception as e:
            logger.error(f"Error extracting entities from section: {str(e)}")
            return {"machinery": [], "subcomponents": [], "spare_parts": []}
    
    def extract_machinery(self, section: DocumentSection) -> List[Machinery]:
        """Extract machinery entities from a section."""
        try:
            # Use LLM for extraction
            prompt = ENTITY_EXTRACTION_PROMPTS["machinery"]
            messages = [
                SystemMessage(content="You are an expert marine engineer specializing in ship machinery identification."),
                HumanMessage(content=prompt.format(text=section.content))
            ]
            
            response = self.llm(messages)
            
            # Parse response
            machinery_data = self._parse_json_response(response.content)
            
            machinery_list = []
            for item in machinery_data:
                try:
                    # Extract specifications using regex
                    specs = self._extract_specifications(section.content)
                    
                    machinery = Machinery(
                        name=item.get("name", "Unknown"),
                        description=item.get("description", ""),
                        type=item.get("type", ""),
                        specifications=specs,
                        confidence_score=0.8  # Default confidence
                    )
                    
                    # Add manufacturer if found
                    manufacturer = self._extract_manufacturer(section.content)
                    if manufacturer:
                        machinery.manufacturer = manufacturer
                    
                    machinery_list.append(machinery)
                    
                except Exception as e:
                    logger.warning(f"Error creating machinery entity: {str(e)}")
                    continue
            
            return machinery_list
            
        except Exception as e:
            logger.error(f"Error extracting machinery: {str(e)}")
            return []
    
    def extract_subcomponents(self, section: DocumentSection) -> List[Subcomponent]:
        """Extract subcomponent entities from a section."""
        try:
            prompt = ENTITY_EXTRACTION_PROMPTS["subcomponents"]
            messages = [
                SystemMessage(content="You are an expert in marine engineering components and assemblies."),
                HumanMessage(content=prompt.format(text=section.content))
            ]
            
            response = self.llm(messages)
            subcomponent_data = self._parse_json_response(response.content)
            
            subcomponent_list = []
            for item in subcomponent_data:
                try:
                    # Extract technical parameters
                    parameters = self._extract_specifications(section.content)
                    
                    subcomponent = Subcomponent(
                        name=item.get("name", "Unknown"),
                        description=item.get("function", ""),
                        parent_machinery=item.get("parent_machinery", ""),
                        parameters=parameters,
                        confidence_score=0.8
                    )
                    
                    # Extract material if mentioned
                    material = self._extract_material(section.content)
                    if material:
                        subcomponent.material = material
                    
                    subcomponent_list.append(subcomponent)
                    
                except Exception as e:
                    logger.warning(f"Error creating subcomponent entity: {str(e)}")
                    continue
            
            return subcomponent_list
            
        except Exception as e:
            logger.error(f"Error extracting subcomponents: {str(e)}")
            return []
    
    def extract_spare_parts(self, section: DocumentSection) -> List[SparePart]:
        """Extract spare part entities from a section."""
        try:
            prompt = ENTITY_EXTRACTION_PROMPTS["spare_parts"]
            messages = [
                SystemMessage(content="You are an expert in marine spare parts and maintenance components."),
                HumanMessage(content=prompt.format(text=section.content))
            ]
            
            response = self.llm(messages)
            spare_parts_data = self._parse_json_response(response.content)
            
            spare_parts_list = []
            for item in spare_parts_data:
                try:
                    spare_part = SparePart(
                        name=item.get("name", "Unknown"),
                        description=item.get("description", ""),
                        part_number=item.get("part_number", ""),
                        parent_subcomponent=item.get("subcomponent", ""),
                        material=item.get("material", ""),
                        size=item.get("size", ""),
                        operating_pressure=item.get("operating_pressure", ""),
                        manufacturer=item.get("manufacturer", ""),
                        drawing_number=item.get("drawing_number", ""),
                        position_number=item.get("position_number", ""),
                        units=item.get("units", "EA"),
                        spare_part_title=item.get("spare_part_title", ""),
                        quantity=item.get("quantity", 1),
                        confidence_score=0.8
                    )
                    
                    # Try to extract part number using regex if not found by LLM
                    if not spare_part.part_number:
                        part_number = self._extract_part_numbers(section.content)
                        if part_number:
                            spare_part.part_number = part_number[0]
                    
                    spare_parts_list.append(spare_part)
                    
                except Exception as e:
                    logger.warning(f"Error creating spare part entity: {str(e)}")
                    continue
            
            return spare_parts_list
            
        except Exception as e:
            logger.error(f"Error extracting spare parts: {str(e)}")
            return []

    def extract_maintenance_jobs(self, section: DocumentSection) -> List[MaintenanceJob]:
        """Extract maintenance job entities from a section."""
        try:
            prompt = ENTITY_EXTRACTION_PROMPTS["maintenance_jobs"]
            messages = [
                SystemMessage(content="You are an expert in marine equipment maintenance and inspection procedures."),
                HumanMessage(content=prompt.format(text=section.content))
            ]

            response = self.llm(messages)
            jobs_data = self._parse_json_response(response.content)

            jobs_list = []
            for item in jobs_data:
                try:
                    job = MaintenanceJob(
                        name=item.get("equipment_name", "Unknown Equipment"),
                        equipment_name=item.get("equipment_name", "Unknown Equipment"),
                        job_action=item.get("job_action", "Maintenance"),
                        frequency=item.get("frequency"),
                        frequency_type=item.get("frequency_type"),
                        job_description=item.get("job_description", ""),
                        section_name=item.get("section_name"),
                        confidence_score=0.8
                    )

                    jobs_list.append(job)

                except Exception as e:
                    logger.warning(f"Error creating maintenance job entity: {str(e)}")
                    continue

            return jobs_list

        except Exception as e:
            logger.error(f"Error extracting maintenance jobs: {str(e)}")
            return []
    
    def _parse_json_response(self, response_text: str) -> List[Dict]:
        """Parse JSON response from LLM, with fallback handling."""
        try:
            # First, try to extract JSON from markdown code blocks
            markdown_match = re.search(r'```(?:json)?\s*(\[.*?\])\s*```', response_text, re.DOTALL)
            if markdown_match:
                json_str = markdown_match.group(1)
            else:
                # Try to extract JSON array from response
                json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                else:
                    # Try parsing the entire response
                    json_str = response_text

            # Clean up common JSON issues from LLM responses
            json_str = self._clean_json_string(json_str)

            return json.loads(json_str)

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {str(e)}, using fallback")
            return self._fallback_entity_extraction(response_text)

    def _clean_json_string(self, json_str: str) -> str:
        """Clean up common JSON formatting issues from LLM responses."""
        # Remove trailing commas before closing braces/brackets
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

        # Replace null with None for Python compatibility, then back to null
        json_str = json_str.replace('null', 'None')
        json_str = json_str.replace('None', 'null')

        # Fix common quote issues
        json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)

        return json_str.strip()
    
    def _fallback_entity_extraction(self, text: str) -> List[Dict]:
        """Fallback entity extraction using regex patterns."""
        entities = []

        # Try to extract individual JSON objects even if the array is malformed
        json_object_pattern = r'\{[^{}]*\}'
        json_objects = re.findall(json_object_pattern, text, re.DOTALL)

        for obj_str in json_objects:
            try:
                # Clean and parse individual JSON object
                obj_str = self._clean_json_string(obj_str)
                obj = json.loads(obj_str)
                if isinstance(obj, dict) and obj.get('name'):
                    entities.append(obj)
            except:
                continue

        # If no JSON objects found, try pattern-based extraction
        if not entities:
            # Look for key-value patterns
            name_patterns = [
                r'name["\']?\s*:\s*["\']([^"\']+)["\']',
                r'Name["\']?\s*:\s*["\']([^"\']+)["\']',
                r'^([A-Z][A-Za-z\s]+(?:Engine|Motor|Pump|Valve|Tank|System|Unit|Assembly))',
            ]

            for pattern in name_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if len(match.strip()) > 2:
                        entities.append({
                            "name": match.strip()[:50],
                            "description": "",
                            "confidence": 0.3
                        })

        return entities[:15]  # Limit to 15 entities
    
    def _extract_specifications(self, text: str) -> Dict[str, Any]:
        """Extract technical specifications using regex patterns."""
        specs = {}
        
        for spec_type, pattern in self.specification_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                specs[spec_type] = matches[0] if isinstance(matches[0], str) else f"{matches[0][0]} {matches[0][1]}"
        
        return specs
    
    def _extract_part_numbers(self, text: str) -> List[str]:
        """Extract part numbers using regex patterns."""
        part_numbers = []
        
        for pattern in self.part_number_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            part_numbers.extend(matches)
        
        return list(set(part_numbers))  # Remove duplicates
    
    def _extract_manufacturer(self, text: str) -> Optional[str]:
        """Extract manufacturer information."""
        # Common manufacturer indicators
        manufacturer_patterns = [
            r'(?:manufactured by|made by|supplier|mfg\.?):?\s*([A-Za-z\s&]+)',
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:brand|make|manufacturer)',
        ]
        
        for pattern in manufacturer_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_material(self, text: str) -> Optional[str]:
        """Extract material information."""
        material_patterns = [
            r'(?:material|made of|constructed from):?\s*([A-Za-z\s\-]+)',
            r'(stainless steel|carbon steel|aluminum|brass|bronze|cast iron|plastic|rubber)',
        ]
        
        for pattern in material_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
    
    def establish_relationships(self, machinery: List[Machinery], 
                             subcomponents: List[Subcomponent], 
                             spare_parts: List[SparePart]) -> None:
        """Establish relationships between entities."""
        try:
            # Link subcomponents to machinery
            for subcomp in subcomponents:
                for machine in machinery:
                    if self._entities_related(machine.name, subcomp.parent_machinery or subcomp.name):
                        machine.add_subcomponent(subcomp.name)
                        subcomp.parent_machinery = machine.name
            
            # Link spare parts to subcomponents
            for part in spare_parts:
                for subcomp in subcomponents:
                    if self._entities_related(subcomp.name, part.parent_subcomponent or part.name):
                        subcomp.add_spare_part(part.name)
                        part.parent_subcomponent = subcomp.name
                        part.parent_machinery = subcomp.parent_machinery
            
            logger.info("Entity relationships established")
            
        except Exception as e:
            logger.error(f"Error establishing relationships: {str(e)}")
    
    def _entities_related(self, entity1: str, entity2: str) -> bool:
        """Check if two entities are related based on name similarity."""
        if not entity1 or not entity2:
            return False
        
        # Simple similarity check
        entity1_words = set(entity1.lower().split())
        entity2_words = set(entity2.lower().split())
        
        # Check for common words
        common_words = entity1_words.intersection(entity2_words)
        
        # Consider related if they share significant words
        return len(common_words) > 0 and len(common_words) >= min(len(entity1_words), len(entity2_words)) * 0.3
